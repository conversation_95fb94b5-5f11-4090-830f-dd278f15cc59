[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"


[project]
name = "dsf"
version = "0.0.1"
description = "A framework for dsf program source code to one file."
readme = "README.md"
requires-python = ">=3.13"
license = { file = "LICENSE" }
keywords = ["dsf", "source"]
authors = [{ name = "Lilin Lao", email = "<EMAIL>" }]
maintainers = [{ name = "Lilin Lao", email = "<EMAIL>" }]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Topic :: Software Development :: Build Tools",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Programming Language :: Python :: 3 :: Only",
]
dependencies = ["fastapi", "pydantic", "uvicorn"]

[project.optional-dependencies]
dev = ["check-manifest"]
test = ["coverage"]

[project.urls]
"Homepage" = "https://github.com/lll9p/dsf"
"Bug Reports" = "https://github.com/lll9p/dsf/issues"
"Source" = "https://github.com/lll9p/dsf/"

[project.scripts]
dsf = "dsf:main"


[tool.setuptools]
package-data = { "dsf" = ["*.dat"] }

[tool.pytest.ini_options]
addopts = "--doctest-modules -rpfs"
disable_test_id_escaping_and_forfeit_all_rights_to_community_support = true

[tool.pyright]
include = ["dsf", "tests"]
exclude = ["**/node_modules", "**/__pycache__", "dist", "build"]
venv = ".venv"
pythonPlatform = "All"
useLibraryCodeForTypes = true
strict = ["**/__init__.py"]
typeCheckingMode = "strict"
analyzeUnannotatedFunctions = true
strictParameterNoneValue = true
# unknown checks
reportUnknownArgumentType = "none"
reportUnknownLambdaType = "none"
reportUnknownMemberType = "none"
reportUnknownParameterType = "warning"
reportUnknownVariableType = "none"
reportConstantRedefinition = "warning"
reportDeprecated = "warning"
reportDuplicateImport = "warning"
reportIncompleteStub = "warning"
reportInconsistentConstructor = "warning"
reportInvalidStubStatement = "warning"
reportMatchNotExhaustive = "warning"
reportMissingParameterType = "warning"
reportMissingTypeArgument = "warning"
reportPrivateUsage = "warning"
reportTypeCommentUsage = "warning"
reportUnnecessaryCast = "warning"
reportUnnecessaryComparison = "warning"
reportUnnecessaryContains = "warning"
reportUnnecessaryIsInstance = "warning"
reportUnusedClass = "warning"
reportUnusedImport = "warning"
reportUnusedFunction = "warning"
reportUnusedVariable = "warning"
reportUntypedBaseClass = "warning"
reportUntypedClassDecorator = "warning"
reportUntypedFunctionDecorator = "warning"
reportUntypedNamedTuple = "warning"
reportImportCycles = "error"
reportIncompatibleMethodOverride = "warning"
reportMissingTypeStubs = "warning"
reportWildcardImportFromLibrary = "error"

[tool.mypy]
strict = true
extra_checks = true
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_unreachable = true
show_error_codes = true
show_column_numbers = true
ignore_missing_imports = true
implicit_reexport = false
disallow_untyped_decorators = false
enable_incomplete_feature = ["NewGenericSyntax"]

[[tool.mypy.overrides]]
module = [
    "pathspec.*",
    "IPython.*",
    "colorama.*",
    "tokenize_rt.*",
    "uvloop.*",
    "_black_version.*",
]
ignore_missing_imports = true


# CI only checks src/, but in case users are running LSP or similar we explicitly ignore
# errors in test data files.
[[tool.mypy.overrides]]
module = ["tests.data.*"]
ignore_errors = true


[tool.ruff]
line-length = 120
lint.select = ["F", "E", "W", "UP", "I"]
lint.ignore = ["F401"]
