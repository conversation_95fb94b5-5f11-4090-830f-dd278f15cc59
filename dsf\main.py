"""
启动AI API代理服务器
"""

import argparse
import os
import sys

import uvicorn

from .proxy import create_app


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI API转发代理服务器")
    parser.add_argument(
        "--target-url",
        type=str,
        default=None,
        help="目标API的基础URL (默认: 从环境变量TARGET_API_URL获取，或使用 https://api.deepseek.com/v1)",
    )
    parser.add_argument("--host", type=str, default="127.0.0.1", help="监听地址 (默认: 127.0.0.1)")
    parser.add_argument("--port", type=int, default=8000, help="监听端口 (默认: 8000)")
    parser.add_argument("--reload", action="store_true", help="启用自动重载 (开发模式)")

    args = parser.parse_args()

    # 设置目标URL
    target_url = args.target_url or os.getenv("TARGET_API_URL", "https://api.deepseek.com/v1")

    print("启动AI API代理服务器...")
    print(f"监听地址: {args.host}:{args.port}")
    print(f"目标API: {target_url}")
    print(f"代理地址: http://{args.host}:{args.port}")
    print("-" * 50)

    # 设置环境变量供应用使用
    os.environ["TARGET_API_URL"] = target_url

    # 启动服务器
    try:
        uvicorn.run("dsf.proxy:app", host=args.host, port=args.port, reload=args.reload, log_level="info")
    except KeyboardInterrupt:
        print("\n服务器已停止")
        sys.exit(0)


if __name__ == "__main__":
    main()
